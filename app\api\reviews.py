"""
Reviews API endpoints.
"""
import logging
from typing import List
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.db.database import get_db
from app.models.book import Book
from app.models.review import Review
from app.schemas.review import ReviewCreate, ReviewResponse
from app.core.exceptions import BookNotFoundException, DatabaseException
from app.services.cache import cache_service

logger = logging.getLogger(__name__)
router = APIRouter()


def update_book_stats(db: Session, book_id: int):
    """
    Update book statistics (average rating and review count).
    """
    try:
        stats = db.query(
            func.avg(Review.rating).label('avg_rating'),
            func.count(Review.id).label('review_count')
        ).filter(Review.book_id == book_id).first()
        
        book = db.query(Book).filter(Book.id == book_id).first()
        if book:
            book.average_rating = round(stats.avg_rating or 0.0, 1)
            book.review_count = stats.review_count or 0
            db.commit()
            
    except Exception as e:
        logger.error(f"Error updating book stats for book {book_id}: {e}")
        db.rollback()


@router.post("/{book_id}/reviews", response_model=ReviewResponse, status_code=status.HTTP_201_CREATED)
async def create_review(
    book_id: int,
    review: ReviewCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new review for a book.
    
    This endpoint:
    1. Validates that the book exists
    2. Creates the review
    3. Updates book statistics (average rating, review count)
    4. Clears relevant cache entries
    """
    try:
        # Check if book exists
        book = db.query(Book).filter(Book.id == book_id).first()
        if not book:
            raise BookNotFoundException(book_id)
        
        # Create new review
        db_review = Review(book_id=book_id, **review.model_dump())
        db.add(db_review)
        db.commit()
        db.refresh(db_review)
        
        # Update book statistics
        update_book_stats(db, book_id)
        
        # Clear relevant cache entries
        cache_service.clear_pattern("books:*")
        cache_service.delete(f"book:{book_id}")
        
        logger.info(f"Created new review: {db_review.id} for book: {book_id}")
        return ReviewResponse.model_validate(db_review)
        
    except BookNotFoundException:
        raise
    except Exception as e:
        logger.error(f"Database error in create_review: {e}")
        db.rollback()
        raise DatabaseException(f"Failed to create review: {str(e)}")


@router.get("/{book_id}/reviews", response_model=List[ReviewResponse])
async def get_book_reviews(
    book_id: int,
    skip: int = Query(0, ge=0, description="Number of reviews to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of reviews to return"),
    sort_by: str = Query("created_at", description="Sort by: created_at, rating"),
    order: str = Query("desc", description="Sort order: asc, desc"),
    db: Session = Depends(get_db)
):
    """
    Get all reviews for a specific book.
    
    This endpoint provides:
    1. Pagination support
    2. Sorting options (by date or rating)
    3. Optimized database queries using indexes
    """
    try:
        # Check if book exists
        book = db.query(Book).filter(Book.id == book_id).first()
        if not book:
            raise BookNotFoundException(book_id)
        
        # Build query with optimized indexes
        query = db.query(Review).filter(Review.book_id == book_id)
        
        # Apply sorting
        if sort_by == "rating":
            if order == "asc":
                query = query.order_by(Review.rating.asc())
            else:
                query = query.order_by(Review.rating.desc())
        else:  # Default to created_at
            if order == "asc":
                query = query.order_by(Review.created_at.asc())
            else:
                query = query.order_by(Review.created_at.desc())
        
        # Apply pagination
        reviews = query.offset(skip).limit(limit).all()
        
        return [ReviewResponse.model_validate(review) for review in reviews]
        
    except BookNotFoundException:
        raise
    except Exception as e:
        logger.error(f"Database error in get_book_reviews: {e}")
        raise DatabaseException(f"Failed to retrieve reviews for book {book_id}: {str(e)}")
