"""
Pydantic schemas for Book model.
"""
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, validator


class BookBase(BaseModel):
    """Base schema for Book."""
    title: str = Field(..., min_length=1, max_length=255, description="Book title")
    author: str = Field(..., min_length=1, max_length=255, description="Book author")
    isbn: Optional[str] = Field(None, max_length=20, description="ISBN number")
    description: Optional[str] = Field(None, description="Book description")
    publication_year: Optional[int] = Field(None, ge=1000, le=2100, description="Publication year")

    @validator('isbn')
    def validate_isbn(cls, v):
        if v is not None:
            # Remove any hyphens or spaces
            isbn = v.replace('-', '').replace(' ', '')
            # Check if it's a valid ISBN-10 or ISBN-13
            if len(isbn) not in [10, 13]:
                raise ValueError('ISBN must be 10 or 13 digits')
            if not isbn.isdigit():
                raise ValueError('ISBN must contain only digits')
        return v


class BookCreate(BookBase):
    """Schema for creating a new book."""

    class Config:
        json_schema_extra = {
            "example": {
                "title": "The Great Gatsby",
                "author": "F. Scott Fitzgerald",
                "isbn": "9780743273565",
                "description": "A classic American novel set in the Jazz Age",
                "publication_year": 1925
            }
        }


class BookUpdate(BaseModel):
    """Schema for updating a book."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    author: Optional[str] = Field(None, min_length=1, max_length=255)
    isbn: Optional[str] = Field(None, max_length=20)
    description: Optional[str] = None
    publication_year: Optional[int] = Field(None, ge=1000, le=2100)


class BookResponse(BookBase):
    """Schema for book response."""
    id: int
    average_rating: float = Field(default=0.0, description="Average rating of the book")
    review_count: int = Field(default=0, description="Number of reviews")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class BookWithReviews(BookResponse):
    """Schema for book response with reviews."""
    reviews: List['ReviewResponse'] = []

    class Config:
        from_attributes = True


# Forward reference resolution
from app.schemas.review import ReviewResponse
BookWithReviews.model_rebuild()
