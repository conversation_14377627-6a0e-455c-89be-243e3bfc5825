"""
Pydantic schemas for Review model.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator, EmailStr


class ReviewBase(BaseModel):
    """Base schema for Review."""
    reviewer_name: str = Field(..., min_length=1, max_length=255, description="Reviewer name")
    reviewer_email: Optional[str] = Field(None, description="Reviewer email")
    rating: float = Field(..., ge=1.0, le=5.0, description="Rating from 1.0 to 5.0")
    title: Optional[str] = Field(None, max_length=255, description="Review title")
    content: Optional[str] = Field(None, description="Review content")

    @validator('rating')
    def validate_rating(cls, v):
        # Round to one decimal place
        return round(v, 1)

    @validator('reviewer_email')
    def validate_email(cls, v):
        if v is not None and v.strip():
            # Basic email validation
            if '@' not in v or '.' not in v.split('@')[-1]:
                raise ValueError('Invalid email format')
        return v


class ReviewCreate(ReviewBase):
    """Schema for creating a new review."""

    class Config:
        json_schema_extra = {
            "example": {
                "reviewer_name": "<PERSON>e",
                "reviewer_email": "<EMAIL>",
                "rating": 4.5,
                "title": "Great book!",
                "content": "I really enjoyed reading this book. The characters were well-developed and the plot was engaging."
            }
        }


class ReviewUpdate(BaseModel):
    """Schema for updating a review."""
    reviewer_name: Optional[str] = Field(None, min_length=1, max_length=255)
    reviewer_email: Optional[str] = None
    rating: Optional[float] = Field(None, ge=1.0, le=5.0)
    title: Optional[str] = Field(None, max_length=255)
    content: Optional[str] = None


class ReviewResponse(ReviewBase):
    """Schema for review response."""
    id: int
    book_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ReviewWithBook(ReviewResponse):
    """Schema for review response with book information."""
    book: 'BookResponse'

    class Config:
        from_attributes = True


# Forward reference resolution
from app.schemas.book import BookResponse
ReviewWithBook.model_rebuild()
