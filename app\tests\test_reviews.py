"""
Unit tests for reviews API endpoints.
"""
import pytest
from fastapi import status


class TestReviewsAPI:
    """Test cases for reviews API endpoints."""
    
    def test_create_review_success(self, client, sample_book_data, sample_review_data):
        """Test successful review creation."""
        # Create a book first
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        # Create a review
        response = client.post(f"/books/{book_id}/reviews", json=sample_review_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["book_id"] == book_id
        assert data["reviewer_name"] == sample_review_data["reviewer_name"]
        assert data["rating"] == sample_review_data["rating"]
        assert data["id"] is not None
    
    def test_create_review_book_not_found(self, client, sample_review_data):
        """Test creating review for non-existent book."""
        response = client.post("/books/999/reviews", json=sample_review_data)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "not found" in response.json()["error"]
    
    def test_create_review_invalid_rating(self, client, sample_book_data, sample_review_data):
        """Test creating review with invalid rating."""
        # Create a book first
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        # Try to create review with invalid rating
        invalid_review = sample_review_data.copy()
        invalid_review["rating"] = 6.0  # Rating should be 1.0-5.0
        
        response = client.post(f"/books/{book_id}/reviews", json=invalid_review)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_create_review_updates_book_stats(self, client, sample_book_data, sample_review_data):
        """Test that creating review updates book statistics."""
        # Create a book first
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        # Create a review
        client.post(f"/books/{book_id}/reviews", json=sample_review_data)
        
        # Check that book stats are updated
        book_response = client.get(f"/books/{book_id}")
        book_data = book_response.json()
        
        assert book_data["review_count"] == 1
        assert book_data["average_rating"] == sample_review_data["rating"]
    
    def test_create_multiple_reviews_average_rating(self, client, sample_book_data, sample_review_data):
        """Test that multiple reviews calculate correct average rating."""
        # Create a book first
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        # Create multiple reviews with different ratings
        review1 = sample_review_data.copy()
        review1["rating"] = 4.0
        client.post(f"/books/{book_id}/reviews", json=review1)
        
        review2 = sample_review_data.copy()
        review2["rating"] = 5.0
        review2["reviewer_name"] = "Another Reviewer"
        client.post(f"/books/{book_id}/reviews", json=review2)
        
        # Check average rating
        book_response = client.get(f"/books/{book_id}")
        book_data = book_response.json()
        
        assert book_data["review_count"] == 2
        assert book_data["average_rating"] == 4.5  # (4.0 + 5.0) / 2
    
    def test_get_book_reviews_empty(self, client, sample_book_data):
        """Test getting reviews for book with no reviews."""
        # Create a book first
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        response = client.get(f"/books/{book_id}/reviews")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == []
    
    def test_get_book_reviews_with_data(self, client, sample_book_data, sample_review_data):
        """Test getting reviews for book with reviews."""
        # Create a book and review
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        client.post(f"/books/{book_id}/reviews", json=sample_review_data)
        
        response = client.get(f"/books/{book_id}/reviews")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]["reviewer_name"] == sample_review_data["reviewer_name"]
    
    def test_get_book_reviews_book_not_found(self, client):
        """Test getting reviews for non-existent book."""
        response = client.get("/books/999/reviews")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "not found" in response.json()["error"]
    
    def test_get_book_reviews_pagination(self, client, sample_book_data, sample_review_data):
        """Test reviews pagination."""
        # Create a book first
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        # Create multiple reviews
        for i in range(5):
            review = sample_review_data.copy()
            review["reviewer_name"] = f"Reviewer {i}"
            client.post(f"/books/{book_id}/reviews", json=review)
        
        # Test pagination
        response = client.get(f"/books/{book_id}/reviews?skip=2&limit=2")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 2
    
    def test_get_book_reviews_sorting(self, client, sample_book_data, sample_review_data):
        """Test reviews sorting by rating."""
        # Create a book first
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        # Create reviews with different ratings
        review1 = sample_review_data.copy()
        review1["rating"] = 3.0
        review1["reviewer_name"] = "Reviewer 1"
        client.post(f"/books/{book_id}/reviews", json=review1)
        
        review2 = sample_review_data.copy()
        review2["rating"] = 5.0
        review2["reviewer_name"] = "Reviewer 2"
        client.post(f"/books/{book_id}/reviews", json=review2)
        
        # Test sorting by rating descending
        response = client.get(f"/books/{book_id}/reviews?sort_by=rating&order=desc")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 2
        assert data[0]["rating"] == 5.0  # Higher rating first
        assert data[1]["rating"] == 3.0
