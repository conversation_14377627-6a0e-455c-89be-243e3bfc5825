"""
Unit tests for books API endpoints.
"""
import pytest
from fastapi import status
from app.models.book import Book


class TestBooksAPI:
    """Test cases for books API endpoints."""
    
    def test_create_book_success(self, client, sample_book_data):
        """Test successful book creation."""
        response = client.post("/books", json=sample_book_data)
        
        assert response.status_code == status.HTTP_201_CREATED
        data = response.json()
        assert data["title"] == sample_book_data["title"]
        assert data["author"] == sample_book_data["author"]
        assert data["isbn"] == sample_book_data["isbn"]
        assert data["id"] is not None
        assert data["average_rating"] == 0.0
        assert data["review_count"] == 0
    
    def test_create_book_duplicate_isbn(self, client, sample_book_data):
        """Test creating book with duplicate ISBN."""
        # Create first book
        client.post("/books", json=sample_book_data)
        
        # Try to create second book with same ISBN
        response = client.post("/books", json=sample_book_data)
        
        assert response.status_code == status.HTTP_409_CONFLICT
        assert "already exists" in response.json()["detail"]
    
    def test_create_book_invalid_data(self, client):
        """Test creating book with invalid data."""
        invalid_data = {
            "title": "",  # Empty title
            "author": "Test Author",
            "publication_year": 3000  # Future year
        }
        
        response = client.post("/books", json=invalid_data)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_get_books_empty(self, client):
        """Test getting books when database is empty."""
        response = client.get("/books")
        
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == []
    
    def test_get_books_with_data(self, client, sample_book_data):
        """Test getting books with data."""
        # Create a book first
        client.post("/books", json=sample_book_data)
        
        response = client.get("/books")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == sample_book_data["title"]
    
    def test_get_books_pagination(self, client, sample_book_data):
        """Test books pagination."""
        # Create multiple books
        for i in range(5):
            book_data = sample_book_data.copy()
            book_data["title"] = f"Book {i}"
            book_data["isbn"] = f"123456789{i}"
            client.post("/books", json=book_data)
        
        # Test pagination
        response = client.get("/books?skip=2&limit=2")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 2
    
    def test_get_books_search(self, client, sample_book_data):
        """Test books search functionality."""
        # Create books with different titles
        book1 = sample_book_data.copy()
        book1["title"] = "Python Programming"
        book1["isbn"] = "1111111111"
        client.post("/books", json=book1)
        
        book2 = sample_book_data.copy()
        book2["title"] = "Java Development"
        book2["isbn"] = "2222222222"
        client.post("/books", json=book2)
        
        # Search for Python
        response = client.get("/books?search=Python")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert "Python" in data[0]["title"]
    
    def test_get_book_by_id_success(self, client, sample_book_data):
        """Test getting book by ID successfully."""
        # Create a book first
        create_response = client.post("/books", json=sample_book_data)
        book_id = create_response.json()["id"]
        
        response = client.get(f"/books/{book_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == book_id
        assert data["title"] == sample_book_data["title"]
        assert "reviews" in data
    
    def test_get_book_by_id_not_found(self, client):
        """Test getting non-existent book by ID."""
        response = client.get("/books/999")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "not found" in response.json()["error"]
    
    def test_get_books_cache_behavior(self, client, sample_book_data):
        """Test that books endpoint uses caching."""
        # Create a book
        client.post("/books", json=sample_book_data)
        
        # First request (cache miss)
        response1 = client.get("/books")
        assert response1.status_code == status.HTTP_200_OK
        
        # Second request (should hit cache)
        response2 = client.get("/books")
        assert response2.status_code == status.HTTP_200_OK
        assert response1.json() == response2.json()
