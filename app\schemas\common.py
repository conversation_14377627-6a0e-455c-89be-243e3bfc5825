"""
Common schemas used across the application.
"""
from typing import Any, Dict, Optional
from pydantic import BaseModel


class ErrorResponse(BaseModel):
    """Standard error response schema."""
    error: str
    details: Dict[str, Any] = {}
    status_code: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "Book not found",
                "details": {"book_id": 123},
                "status_code": 404
            }
        }


class HealthResponse(BaseModel):
    """Health check response schema."""
    status: str
    cache_available: bool
    version: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "cache_available": True,
                "version": "1.0.0"
            }
        }


class MessageResponse(BaseModel):
    """Generic message response schema."""
    message: str
    version: Optional[str] = None
    docs: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Welcome to Book Review Service",
                "version": "1.0.0",
                "docs": "/docs"
            }
        }
