"""
Review model definition.
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, ForeignKey, Index
from sqlalchemy.orm import relationship
from app.db.database import Base


class Review(Base):
    """
    Review model representing a book review in the system.
    """
    __tablename__ = "reviews"

    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), nullable=False)
    reviewer_name = Column(String(255), nullable=False)
    reviewer_email = Column(String(255), nullable=True)
    rating = Column(Float, nullable=False)  # Rating from 1.0 to 5.0
    title = Column(String(255), nullable=True)
    content = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with book
    book = relationship("Book", back_populates="reviews")

    # Index for optimizing fetching reviews by book
    __table_args__ = (
        Index('idx_reviews_book_id', 'book_id'),
        Index('idx_reviews_book_id_created_at', 'book_id', 'created_at'),
        Index('idx_reviews_rating', 'rating'),
    )

    def __repr__(self):
        return f"<Review(id={self.id}, book_id={self.book_id}, rating={self.rating})>"
