"""
Books API endpoints.
"""
import json
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.db.database import get_db
from app.models.book import Book
from app.models.review import Review
from app.schemas.book import BookCreate, BookResponse, BookWithReviews
from app.core.exceptions import BookNotFoundException, DatabaseException
from app.services.cache import cache_service

logger = logging.getLogger(__name__)
router = APIRouter()


def update_book_stats(db: Session, book_id: int):
    """
    Update book statistics (average rating and review count).
    """
    try:
        stats = db.query(
            func.avg(Review.rating).label('avg_rating'),
            func.count(Review.id).label('review_count')
        ).filter(Review.book_id == book_id).first()
        
        book = db.query(Book).filter(Book.id == book_id).first()
        if book:
            book.average_rating = round(stats.avg_rating or 0.0, 1)
            book.review_count = stats.review_count or 0
            db.commit()
            
    except Exception as e:
        logger.error(f"Error updating book stats for book {book_id}: {e}")
        db.rollback()


@router.get(
    "",
    response_model=List[BookResponse],
    summary="Get all books",
    description="Retrieve a paginated list of books with optional search functionality. Uses cache-aside pattern for optimal performance.",
    responses={
        200: {"description": "List of books retrieved successfully"},
        500: {"description": "Database or cache error"}
    }
)
async def get_books(
    skip: int = Query(0, ge=0, description="Number of books to skip for pagination"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of books to return (1-1000)"),
    search: Optional[str] = Query(None, description="Search term to filter books by title or author"),
    db: Session = Depends(get_db)
):
    """
    Get all books with caching support.
    
    This endpoint implements cache-aside pattern:
    1. First attempts to read from cache
    2. If cache miss, reads from database and populates cache
    3. Handles cache failures gracefully
    """
    # Create cache key
    cache_key = f"books:skip:{skip}:limit:{limit}:search:{search or 'none'}"
    
    # Try to get from cache first
    cached_books = cache_service.get(cache_key)
    if cached_books is not None:
        logger.info(f"Cache hit for key: {cache_key}")
        return cached_books
    
    logger.info(f"Cache miss for key: {cache_key}")
    
    try:
        # Query database
        query = db.query(Book)
        
        # Apply search filter if provided
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Book.title.ilike(search_term)) | 
                (Book.author.ilike(search_term))
            )
        
        # Apply pagination
        books = query.offset(skip).limit(limit).all()
        
        # Convert to response format
        books_response = [BookResponse.model_validate(book) for book in books]
        
        # Cache the result
        cache_success = cache_service.set(cache_key, [book.model_dump() for book in books_response])
        if cache_success:
            logger.info(f"Cached books for key: {cache_key}")
        else:
            logger.warning(f"Failed to cache books for key: {cache_key}")
        
        return books_response
        
    except Exception as e:
        logger.error(f"Database error in get_books: {e}")
        raise DatabaseException(f"Failed to retrieve books: {str(e)}")


@router.get(
    "/{book_id}",
    response_model=BookWithReviews,
    summary="Get a specific book",
    description="Retrieve a book by its ID along with all associated reviews.",
    responses={
        200: {"description": "Book retrieved successfully"},
        404: {"description": "Book not found"},
        500: {"description": "Database error"}
    }
)
async def get_book(
    book_id: int = Query(..., description="Unique identifier of the book"),
    db: Session = Depends(get_db)
):
    """
    Get a specific book by ID with its reviews.
    """
    try:
        book = db.query(Book).filter(Book.id == book_id).first()
        if not book:
            raise BookNotFoundException(book_id)
        
        return BookWithReviews.model_validate(book)
        
    except BookNotFoundException:
        raise
    except Exception as e:
        logger.error(f"Database error in get_book: {e}")
        raise DatabaseException(f"Failed to retrieve book {book_id}: {str(e)}")


@router.post(
    "",
    response_model=BookResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new book",
    description="Add a new book to the collection. ISBN must be unique if provided.",
    responses={
        201: {"description": "Book created successfully"},
        409: {"description": "Book with this ISBN already exists"},
        422: {"description": "Validation error"},
        500: {"description": "Database error"}
    }
)
async def create_book(
    book: BookCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new book.
    """
    try:
        # Check if book with same ISBN already exists
        if book.isbn:
            existing_book = db.query(Book).filter(Book.isbn == book.isbn).first()
            if existing_book:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Book with ISBN {book.isbn} already exists"
                )
        
        # Create new book
        db_book = Book(**book.model_dump())
        db.add(db_book)
        db.commit()
        db.refresh(db_book)
        
        # Clear books cache
        cache_service.clear_pattern("books:*")
        
        logger.info(f"Created new book: {db_book.id}")
        return BookResponse.model_validate(db_book)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Database error in create_book: {e}")
        db.rollback()
        raise DatabaseException(f"Failed to create book: {str(e)}")
