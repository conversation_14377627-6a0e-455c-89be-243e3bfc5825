"""
Integration tests covering cache behavior, database interactions, and end-to-end workflows.
"""
import pytest
from unittest.mock import patch, MagicMock
from fastapi import status
from app.services.cache import cache_service


class TestCacheIntegration:
    """Integration tests for cache behavior."""
    
    def test_cache_miss_scenario(self, client, sample_book_data):
        """Test cache miss scenario and database fallback."""
        # Clear cache to ensure miss
        cache_service.clear_pattern("*")
        
        # Create a book
        client.post("/books", json=sample_book_data)
        
        # First request should be cache miss
        with patch.object(cache_service, 'get', return_value=None) as mock_get:
            with patch.object(cache_service, 'set', return_value=True) as mock_set:
                response = client.get("/books")
                
                assert response.status_code == status.HTTP_200_OK
                assert len(response.json()) == 1
                
                # Verify cache was checked and set
                mock_get.assert_called_once()
                mock_set.assert_called_once()
    
    def test_cache_hit_scenario(self, client, sample_book_data):
        """Test cache hit scenario."""
        # Create a book
        client.post("/books", json=sample_book_data)
        
        # First request to populate cache
        response1 = client.get("/books")
        assert response1.status_code == status.HTTP_200_OK
        
        # Mock cache to return data
        cached_data = response1.json()
        with patch.object(cache_service, 'get', return_value=cached_data) as mock_get:
            response2 = client.get("/books")
            
            assert response2.status_code == status.HTTP_200_OK
            assert response2.json() == cached_data
            mock_get.assert_called_once()
    
    def test_cache_failure_fallback(self, client, sample_book_data):
        """Test that API works when cache is completely down."""
        # Create a book
        client.post("/books", json=sample_book_data)
        
        # Mock cache service to simulate failure
        with patch.object(cache_service, 'get', side_effect=Exception("Cache down")):
            with patch.object(cache_service, 'set', side_effect=Exception("Cache down")):
                response = client.get("/books")
                
                # Should still work, falling back to database
                assert response.status_code == status.HTTP_200_OK
                assert len(response.json()) == 1
    
    def test_cache_invalidation_on_create(self, client, sample_book_data):
        """Test that cache is invalidated when new book is created."""
        # First, populate cache
        response1 = client.get("/books")
        assert response1.status_code == status.HTTP_200_OK
        assert len(response1.json()) == 0
        
        # Create a book (should invalidate cache)
        with patch.object(cache_service, 'clear_pattern') as mock_clear:
            client.post("/books", json=sample_book_data)
            mock_clear.assert_called_with("books:*")
        
        # Next request should show new book
        response2 = client.get("/books")
        assert response2.status_code == status.HTTP_200_OK
        assert len(response2.json()) == 1


class TestEndToEndWorkflows:
    """End-to-end integration tests."""
    
    def test_complete_book_review_workflow(self, client, sample_book_data, sample_review_data):
        """Test complete workflow: create book, add reviews, verify stats."""
        # Step 1: Create a book
        book_response = client.post("/books", json=sample_book_data)
        assert book_response.status_code == status.HTTP_201_CREATED
        book_id = book_response.json()["id"]
        
        # Step 2: Verify book appears in list
        books_response = client.get("/books")
        assert books_response.status_code == status.HTTP_200_OK
        assert len(books_response.json()) == 1
        
        # Step 3: Add multiple reviews
        review1 = sample_review_data.copy()
        review1["rating"] = 4.0
        review1_response = client.post(f"/books/{book_id}/reviews", json=review1)
        assert review1_response.status_code == status.HTTP_201_CREATED
        
        review2 = sample_review_data.copy()
        review2["rating"] = 5.0
        review2["reviewer_name"] = "Another Reviewer"
        review2_response = client.post(f"/books/{book_id}/reviews", json=review2)
        assert review2_response.status_code == status.HTTP_201_CREATED
        
        # Step 4: Verify book statistics are updated
        updated_book_response = client.get(f"/books/{book_id}")
        assert updated_book_response.status_code == status.HTTP_200_OK
        book_data = updated_book_response.json()
        
        assert book_data["review_count"] == 2
        assert book_data["average_rating"] == 4.5
        assert len(book_data["reviews"]) == 2
        
        # Step 5: Verify reviews can be retrieved separately
        reviews_response = client.get(f"/books/{book_id}/reviews")
        assert reviews_response.status_code == status.HTTP_200_OK
        assert len(reviews_response.json()) == 2
    
    def test_search_and_pagination_workflow(self, client, sample_book_data):
        """Test search and pagination functionality."""
        # Create multiple books with different titles
        books_data = [
            {"title": "Python Programming", "author": "Author 1", "isbn": "1111111111"},
            {"title": "Java Development", "author": "Author 2", "isbn": "2222222222"},
            {"title": "Python Advanced", "author": "Author 3", "isbn": "3333333333"},
            {"title": "JavaScript Basics", "author": "Author 4", "isbn": "4444444444"},
        ]
        
        for book_data in books_data:
            response = client.post("/books", json=book_data)
            assert response.status_code == status.HTTP_201_CREATED
        
        # Test search functionality
        search_response = client.get("/books?search=Python")
        assert search_response.status_code == status.HTTP_200_OK
        search_results = search_response.json()
        assert len(search_results) == 2
        for book in search_results:
            assert "Python" in book["title"]
        
        # Test pagination
        page1_response = client.get("/books?skip=0&limit=2")
        assert page1_response.status_code == status.HTTP_200_OK
        assert len(page1_response.json()) == 2
        
        page2_response = client.get("/books?skip=2&limit=2")
        assert page2_response.status_code == status.HTTP_200_OK
        assert len(page2_response.json()) == 2
        
        # Verify no overlap between pages
        page1_ids = {book["id"] for book in page1_response.json()}
        page2_ids = {book["id"] for book in page2_response.json()}
        assert page1_ids.isdisjoint(page2_ids)
    
    def test_error_handling_workflow(self, client, sample_review_data):
        """Test error handling across different scenarios."""
        # Test 1: Try to get non-existent book
        response = client.get("/books/999")
        assert response.status_code == status.HTTP_404_NOT_FOUND
        error_data = response.json()
        assert "error" in error_data
        assert "not found" in error_data["error"]
        
        # Test 2: Try to add review to non-existent book
        response = client.post("/books/999/reviews", json=sample_review_data)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
        # Test 3: Try to create book with invalid data
        invalid_book = {"title": "", "author": "Test"}  # Empty title
        response = client.post("/books", json=invalid_book)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        
        # Test 4: Try to create review with invalid rating
        # First create a valid book
        valid_book = {"title": "Test Book", "author": "Test Author"}
        book_response = client.post("/books", json=valid_book)
        book_id = book_response.json()["id"]
        
        invalid_review = sample_review_data.copy()
        invalid_review["rating"] = 6.0  # Invalid rating
        response = client.post(f"/books/{book_id}/reviews", json=invalid_review)
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


class TestDatabaseIndexes:
    """Test that database indexes are working correctly."""
    
    def test_book_search_performance(self, client):
        """Test that book search uses indexes efficiently."""
        # Create many books to test index usage
        for i in range(50):
            book_data = {
                "title": f"Book Title {i}",
                "author": f"Author {i}",
                "isbn": f"123456789{i:02d}"
            }
            client.post("/books", json=book_data)
        
        # Search should still be fast with indexes
        response = client.get("/books?search=Book Title 25")
        assert response.status_code == status.HTTP_200_OK
        results = response.json()
        assert len(results) == 1
        assert "Book Title 25" in results[0]["title"]
    
    def test_review_retrieval_performance(self, client, sample_book_data, sample_review_data):
        """Test that review retrieval uses book_id index efficiently."""
        # Create a book
        book_response = client.post("/books", json=sample_book_data)
        book_id = book_response.json()["id"]
        
        # Create many reviews
        for i in range(20):
            review = sample_review_data.copy()
            review["reviewer_name"] = f"Reviewer {i}"
            client.post(f"/books/{book_id}/reviews", json=review)
        
        # Retrieving reviews should be fast with book_id index
        response = client.get(f"/books/{book_id}/reviews")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 20
