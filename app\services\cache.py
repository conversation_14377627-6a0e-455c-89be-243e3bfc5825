"""
Cache service implementation with Redis fallback.
"""
import json
import logging
from typing import Any, Optional
from app.core.config import settings
from app.core.exceptions import CacheException

logger = logging.getLogger(__name__)


class CacheService:
    """
    Cache service with Redis backend and fallback handling.
    """
    
    def __init__(self):
        self.redis_client = None
        self.enabled = settings.redis_enabled
        self._initialize_redis()
    
    def _initialize_redis(self):
        """Initialize Redis connection with fallback."""
        if not self.enabled:
            logger.info("Cache is disabled")
            return

        try:
            import redis
            from fakeredis import FakeRedis

            # Use FakeRedis for development/testing
            if "fake" in settings.redis_url.lower() or settings.debug:
                self.redis_client = FakeRedis(decode_responses=True)
                logger.info("Using FakeRedis for caching")
            else:
                self.redis_client = redis.from_url(
                    settings.redis_url,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                # Test connection
                self.redis_client.ping()
                logger.info("Redis connection established")

        except Exception as e:
            logger.warning(f"Failed to initialize Redis: {e}. Cache will be disabled.")
            self.redis_client = None
            self.enabled = False
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found or cache is unavailable
        """
        if not self.enabled or not self.redis_client:
            return None
            
        try:
            value = self.redis_client.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.warning(f"Cache get error for key '{key}': {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (defaults to settings.cache_ttl)
            
        Returns:
            True if successful, False otherwise
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            ttl = ttl or settings.cache_ttl
            serialized_value = json.dumps(value, default=str)
            self.redis_client.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            logger.warning(f"Cache set error for key '{key}': {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        Delete value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if successful, False otherwise
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            self.redis_client.delete(key)
            return True
        except Exception as e:
            logger.warning(f"Cache delete error for key '{key}': {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> bool:
        """
        Clear all keys matching pattern.
        
        Args:
            pattern: Key pattern (e.g., "books:*")
            
        Returns:
            True if successful, False otherwise
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
            return True
        except Exception as e:
            logger.warning(f"Cache clear pattern error for pattern '{pattern}': {e}")
            return False
    
    def is_available(self) -> bool:
        """
        Check if cache is available.
        
        Returns:
            True if cache is available, False otherwise
        """
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            self.redis_client.ping()
            return True
        except Exception:
            return False


# Global cache service instance
cache_service = CacheService()
