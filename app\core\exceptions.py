"""
Custom exception classes for the application.
"""
from typing import Any, Dict, Optional


class BookReviewException(Exception):
    """Base exception class for the Book Review service."""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class BookNotFoundException(BookReviewException):
    """Exception raised when a book is not found."""
    
    def __init__(self, book_id: int):
        super().__init__(
            message=f"Book with id {book_id} not found",
            status_code=404,
            details={"book_id": book_id}
        )


class ValidationException(BookReviewException):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=422,
            details=details
        )


class CacheException(BookReviewException):
    """Exception raised for cache-related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Cache error: {message}",
            status_code=500,
            details=details
        )


class DatabaseException(BookReviewException):
    """Exception raised for database-related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Database error: {message}",
            status_code=500,
            details=details
        )
